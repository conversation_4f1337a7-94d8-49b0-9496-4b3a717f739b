<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ExternalInvoiceService;

class RetryExternalOrders extends Command
{
    protected $signature = 'orders:retry-external';
    protected $description = 'Retry sending failed orders to external system';

    public function handle()
    {
        $this->info('Starting to retry failed external orders...');
        
        $service = new ExternalInvoiceService();
        $results = $service->retryFailedOrders();
        
        $this->info("Total orders processed: {$results['total']}");
        $this->info("Successfully sent: {$results['success']}");
        $this->info("Failed to send: {$results['failed']}");
        
        if (!empty($results['errors'])) {
            $this->error('Errors:');
            foreach ($results['errors'] as $error) {
                $this->error($error);
            }
        }
        
        $this->info('Retry process completed.');
        
        return 0;
    }
}