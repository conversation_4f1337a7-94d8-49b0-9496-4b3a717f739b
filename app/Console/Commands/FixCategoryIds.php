<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Item;
use App\Models\Category;

class FixCategoryIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'items:fix-category-ids {--dry-run : عرض التغييرات فقط دون تطبيقها}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إصلاح حقل category_ids لجميع المنتجات ليحتوي على جميع الأقسام من الجذر إلى القسم المباشر';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 تشغيل في وضع المعاينة - لن يتم تطبيق أي تغييرات');
        } else {
            $this->info('🔧 بدء إصلاح category_ids للمنتجات...');
        }

        $items = Item::whereNotNull('category_id')->get();
        $totalItems = $items->count();
        $fixedItems = 0;
        $errorItems = 0;

        $this->info("📊 إجمالي المنتجات: {$totalItems}");

        $progressBar = $this->output->createProgressBar($totalItems);
        $progressBar->start();

        foreach ($items as $item) {
            try {
                // بناء category_ids الصحيح
                $correctCategoryIds = Category::buildCategoryIds($item->category_id);

                // مقارنة مع القيمة الحالية
                if ($item->category_ids !== $correctCategoryIds) {
                    $this->newLine();
                    $this->info("📝 المنتج: {$item->name} (ID: {$item->id})");
                    $this->line("   القديم: {$item->category_ids}");
                    $this->line("   الجديد: {$correctCategoryIds}");

                    if (!$isDryRun) {
                        $item->category_ids = $correctCategoryIds;
                        $item->save();
                        $this->info("   ✅ تم التحديث");
                    } else {
                        $this->info("   🔍 سيتم التحديث");
                    }

                    $fixedItems++;
                }
            } catch (\Exception $e) {
                $this->newLine();
                $this->error("❌ خطأ في المنتج {$item->name} (ID: {$item->id}): {$e->getMessage()}");
                $errorItems++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // عرض النتائج
        $this->info("📈 النتائج:");
        $this->line("   إجمالي المنتجات: {$totalItems}");
        $this->line("   المنتجات التي تحتاج إصلاح: {$fixedItems}");
        $this->line("   الأخطاء: {$errorItems}");

        if ($isDryRun && $fixedItems > 0) {
            $this->newLine();
            $this->info("💡 لتطبيق التغييرات، قم بتشغيل الأمر بدون --dry-run:");
            $this->line("   php artisan items:fix-category-ids");
        } elseif (!$isDryRun && $fixedItems > 0) {
            $this->newLine();
            $this->info("✅ تم إصلاح {$fixedItems} منتج بنجاح!");
        } elseif ($fixedItems === 0) {
            $this->newLine();
            $this->info("✨ جميع المنتجات لديها category_ids صحيح!");
        }

        return 0;
    }
}
