<?php

namespace App\Http;

use App\Http\Middleware\ActivationCheckMiddleware;
use App\Http\Middleware\InstallationMiddleware;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        // \App\Http\Middleware\TrustProxies::class,
        // \Fruitcake\Cors\HandleCors::class,
        Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \Illuminate\Http\Middleware\HandleCors::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            Middleware\Localization::class,
            Middleware\FoodModuleDetector::class,
        ],

        'api' => [
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'admin' => Middleware\AdminMiddleware::class,
        'vendor' => Middleware\VendorMiddleware::class,
        'vendor.api' => Middleware\VendorTokenIsValid::class,
        'dm.api' => Middleware\DmTokenIsValid::class,
        'module' => Middleware\ModulePermissionMiddleware::class,
        'installation-check' => InstallationMiddleware::class,
        'actch' => ActivationCheckMiddleware::class,
        'localization' => Middleware\LocalizationMiddleware::class,
        'module-check' => Middleware\ModuleCheckMiddleware::class,
        'current-module' => Middleware\CurrentModule::class,
        'apiGuestCheck' => Middleware\APIGuestMiddleware::class,
        'subscription' => Middleware\Subscription::class,
        'admin-rental-module' => Middleware\AdminRentalModuleCheckMiddleware::class,
        'provider-rental-module' => Middleware\ProviderRentalModuleCheckMiddleware::class,
        'food-detector' => Middleware\FoodModuleDetector::class,
    ];
}
