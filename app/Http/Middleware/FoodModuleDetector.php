<?php

namespace App\Http\Middleware;

use App\CentralLogics\Helpers;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class FoodModuleDetector
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // تحديد نوع المودول بناءً على السياق
        $this->detectModuleType($request);

        return $next($request);
    }

    /**
     * تحديد نوع المودول بناءً على السياق
     */
    private function detectModuleType(Request $request)
    {
        // التحقق من وجود category_id في الطلب
        $categoryId = $request->get('category_id') ?? $request->get('category_ids');

        if ($categoryId) {
            // إذا كان category_ids مصفوفة أو JSON
            if (is_string($categoryId) && str_contains($categoryId, '[')) {
                $categoryIds = json_decode($categoryId, true);
                if (is_array($categoryIds) && !empty($categoryIds)) {
                    $categoryId = $categoryIds[0];
                }
            } elseif (is_array($categoryId) && !empty($categoryId)) {
                $categoryId = $categoryId[0];
            }

            // التحقق من أن القسم ينتمي للـ Food
            if (Helpers::isFoodCategory($categoryId)) {
                // تعيين نوع المودول كـ Food مؤقتاً للطلب الحالي
                config(['module.current_module_type' => 'food']);
                $request->attributes->set('is_food_context', true);
            }
        }

        // التحقق من المنتجات في الطلب
        $itemId = $request->get('item_id') ?? $request->get('product_id');
        if ($itemId) {
            $item = \App\Models\Item::find($itemId);
            if ($item && $item->isFoodItem()) {
                config(['module.current_module_type' => 'food']);
                $request->attributes->set('is_food_context', true);
            }
        }
    }
}
