@extends('layouts.admin.app')

@section('title', 'استيراد المنتجات من API')

@push('css_or_js')
<style>
    .progress-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .progress {
        height: 30px;
        border-radius: 15px;
        overflow: hidden;
        background-color: #e9ecef;
    }
    
    .progress-bar {
        transition: width 0.3s ease;
        font-weight: bold;
        line-height: 30px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin: 10px 0;
    }
    
    .api-stats-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin: 10px 0;
    }
    
    .import-log {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 15px;
        max-height: 400px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 14px;
    }
    
    .log-entry {
        margin: 5px 0;
        padding: 5px;
        border-radius: 5px;
    }
    
    .log-info { background-color: #d1ecf1; color: #0c5460; }
    .log-success { background-color: #d4edda; color: #155724; }
    .log-warning { background-color: #fff3cd; color: #856404; }
    .log-error { background-color: #f8d7da; color: #721c24; }
    
    .btn-import {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-import:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .btn-import:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
    }
</style>
@endpush

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-header-title">
            <span class="page-header-icon">
                <i class="tio-cloud-download"></i>
            </span>
            <span>استيراد المنتجات من API</span>
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{route('admin.dashboard')}}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{route('admin.item.list')}}">المنتجات</a></li>
                <li class="breadcrumb-item active">استيراد من API</li>
            </ol>
        </nav>
    </div>

    <!-- Current Stats -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{$stats['total_items']}}</h3>
                <p class="mb-0">المنتجات الحالية</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{$stats['total_categories']}}</h3>
                <p class="mb-0">الأقسام الحالية</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{$stats['total_units']}}</h3>
                <p class="mb-0">الوحدات الحالية</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{$stats['total_addons']}}</h3>
                <p class="mb-0">الإضافات الحالية</p>
            </div>
        </div>
    </div>

    <!-- API Stats -->
    <div class="row" id="api-stats-container" style="display: none;">
        <div class="col-md-3">
            <div class="api-stats-card text-center">
                <h3 id="api-total-items">-</h3>
                <p class="mb-0">منتجات في API</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="api-stats-card text-center">
                <h3 id="api-items-with-addons">-</h3>
                <p class="mb-0">منتجات لها إضافات</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="api-stats-card text-center">
                <h3 id="api-total-addons">-</h3>
                <p class="mb-0">إجمالي الإضافات</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="api-stats-card text-center">
                <h3 id="api-unique-categories">-</h3>
                <p class="mb-0">أقسام مطلوبة</p>
            </div>
        </div>
    </div>

    <!-- Import Controls -->
    <div class="card">
        <div class="card-body text-center">
            <h4>استيراد المنتجات من النظام الخارجي</h4>
            <p class="text-muted">سيتم جلب المنتجات من API الخارجي ومعالجتها تدريجياً</p>
            
            <div class="mt-4">
                <button type="button" class="btn btn-info mr-3" onclick="getApiStats()">
                    <i class="tio-refresh"></i> فحص بيانات API
                </button>
                <button type="button" class="btn btn-import" onclick="confirmImport()" id="import-btn">
                    <i class="tio-cloud-download mr-2"></i> بدء الاستيراد
                </button>
            </div>
        </div>
    </div>

    <!-- Progress Section -->
    <div class="progress-container" id="progress-container" style="display: none;">
        <h5>تقدم الاستيراد</h5>
        <div class="progress mb-3">
            <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="progress-bar">
                0%
            </div>
        </div>
        
        <div class="row text-center">
            <div class="col-md-2">
                <strong id="processed-count">0</strong>
                <br><small>تم معالجتها</small>
            </div>
            <div class="col-md-2">
                <strong id="imported-count">0</strong>
                <br><small>تم استيرادها</small>
            </div>
            <div class="col-md-2">
                <strong id="updated-count">0</strong>
                <br><small>تم تحديثها</small>
            </div>
            <div class="col-md-2">
                <strong id="skipped-count">0</strong>
                <br><small>تم تخطيها</small>
            </div>
            <div class="col-md-2">
                <strong id="errors-count">0</strong>
                <br><small>أخطاء</small>
            </div>
            <div class="col-md-2">
                <strong id="total-count">0</strong>
                <br><small>الإجمالي</small>
            </div>
        </div>
    </div>

    <!-- Import Log -->
    <div class="card" id="log-container" style="display: none;">
        <div class="card-header">
            <h5>سجل الاستيراد</h5>
        </div>
        <div class="card-body">
            <div class="import-log" id="import-log">
                <!-- سيتم إضافة السجل هنا -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('script_2')
<script>
let importInProgress = false;

function getApiStats() {
    if (importInProgress) {
        Swal.fire('تحذير', 'الاستيراد قيد التنفيذ حالياً', 'warning');
        return;
    }

    Swal.fire({
        title: 'جاري فحص بيانات API...',
        text: 'يرجى الانتظار',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '{{ route("admin.item.import.api-stats") }}',
        method: 'GET',
        success: function(response) {
            Swal.close();
            
            if (response.success) {
                displayApiStats(response.stats);
                Swal.fire('نجح!', 'تم جلب إحصائيات API بنجاح', 'success');
            } else {
                Swal.fire('خطأ', response.message, 'error');
            }
        },
        error: function() {
            Swal.close();
            Swal.fire('خطأ', 'حدث خطأ أثناء جلب الإحصائيات', 'error');
        }
    });
}

function displayApiStats(stats) {
    $('#api-total-items').text(stats.total_api_items);
    $('#api-items-with-addons').text(stats.items_with_addons);
    $('#api-total-addons').text(stats.total_addons);
    $('#api-unique-categories').text(stats.unique_categories);
    $('#api-stats-container').show();
}

function confirmImport() {
    if (importInProgress) {
        Swal.fire('تحذير', 'الاستيراد قيد التنفيذ حالياً', 'warning');
        return;
    }

    Swal.fire({
        title: 'هل أنت متأكد؟',
        html: 'سيتم استيراد المنتجات من API الخارجي.<br>' +
              '<strong>تحذير:</strong> هذه العملية قد تستغرق وقتاً طويلاً.<br>' +
              'لا تغلق الصفحة أثناء الاستيراد.',
        type: 'warning',
        showCancelButton: true,
        cancelButtonColor: 'default',
        confirmButtonColor: '#FC6A57',
        cancelButtonText: 'إلغاء',
        confirmButtonText: 'نعم، ابدأ الاستيراد!',
        reverseButtons: true
    }).then((result) => {
        if (result.value) {
            startImport();
        }
    });
}

function startImport() {
    importInProgress = true;
    $('#import-btn').prop('disabled', true).html('<i class="tio-sync fa-spin mr-2"></i> جاري الاستيراد...');
    $('#progress-container').show();
    $('#log-container').show();
    
    addLogEntry('بدء عملية الاستيراد...', 'info');

    $.ajax({
        url: '{{ route("admin.item.import.start") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                $('#total-count').text(response.total_items);
                addLogEntry(`تم تحضير ${response.total_items} منتج للاستيراد`, 'success');
                processBatch();
            } else {
                importFailed(response.message);
            }
        },
        error: function() {
            importFailed('حدث خطأ أثناء بدء الاستيراد');
        }
    });
}

function processBatch() {
    $.ajax({
        url: '{{ route("admin.item.import.process-batch") }}',
        method: 'POST',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                updateProgress(response.progress);
                
                if (response.batch_results) {
                    addLogEntry(`دفعة: +${response.batch_results.imported} استيراد، +${response.batch_results.updated} تحديث، +${response.batch_results.skipped} تخطي، +${response.batch_results.errors} خطأ`, 'info');
                }

                if (response.completed) {
                    if (response.message) {
                        addLogEntry(response.message, 'success');
                    }
                    importCompleted(response.progress);
                } else {
                    // معالجة الدفعة التالية
                    setTimeout(processBatch, 500);
                }
            } else {
                importFailed(response.message);
            }
        },
        error: function() {
            importFailed('حدث خطأ أثناء معالجة الدفعة');
        }
    });
}

function updateProgress(progress) {
    const percentage = Math.round((progress.processed / progress.total) * 100);
    $('#progress-bar').css('width', percentage + '%').text(percentage + '%');
    
    $('#processed-count').text(progress.processed);
    $('#imported-count').text(progress.imported);
    $('#updated-count').text(progress.updated);
    $('#skipped-count').text(progress.skipped);
    $('#errors-count').text(progress.errors);
}

function importCompleted(progress) {
    importInProgress = false;
    $('#import-btn').prop('disabled', false).html('<i class="tio-cloud-download mr-2"></i> بدء الاستيراد');

    addLogEntry('تم إكمال الاستيراد بنجاح!', 'success');
    addLogEntry('تم تنفيذ إصلاح category_ids تلقائياً', 'success');

    Swal.fire({
        title: 'تم الاستيراد بنجاح!',
        html: `تم استيراد ${progress.imported} منتج جديد<br>` +
              `تم تحديث ${progress.updated} منتج موجود<br>` +
              `تم تخطي ${progress.skipped} منتج<br>` +
              `حدثت ${progress.errors} أخطاء<br><br>` +
              `<i class="tio-checkmark-circle text-success"></i> تم إصلاح category_ids تلقائياً`,
        type: 'success',
        confirmButtonText: 'موافق'
    });
}

function importFailed(message) {
    importInProgress = false;
    $('#import-btn').prop('disabled', false).html('<i class="tio-cloud-download mr-2"></i> بدء الاستيراد');
    
    addLogEntry('فشل الاستيراد: ' + message, 'error');
    Swal.fire('خطأ', message, 'error');
}

function addLogEntry(message, type) {
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    const logEntry = `<div class="log-entry log-${type}">[${timestamp}] ${message}</div>`;
    $('#import-log').append(logEntry);
    $('#import-log').scrollTop($('#import-log')[0].scrollHeight);
}

// منع إغلاق الصفحة أثناء الاستيراد
window.addEventListener('beforeunload', function(e) {
    if (importInProgress) {
        e.preventDefault();
        e.returnValue = 'الاستيراد قيد التنفيذ. هل أنت متأكد من إغلاق الصفحة؟';
    }
});
</script>
@endpush
