<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->timestamp('hidden_until')->nullable()->after('updated_at')->comment('وقت إخفاء المنتج حتى هذا التاريخ');
            $table->timestamp('out_of_stock_since')->nullable()->after('hidden_until')->comment('تاريخ نفاد المخزون');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->dropColumn(['hidden_until', 'out_of_stock_since']);
        });
    }
};
